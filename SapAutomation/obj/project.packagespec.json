"restore":{"projectUniqueName":"D:\\Dati\\Dropbox\\Sviluppo\\Web\\AnafSapAutomation\\SapAutomation\\SapAutomation.csproj","projectName":"SapAutomation","projectPath":"D:\\Dati\\Dropbox\\Sviluppo\\Web\\AnafSapAutomation\\SapAutomation\\SapAutomation.csproj","outputPath":"D:\\Dati\\Dropbox\\Sviluppo\\Web\\AnafSapAutomation\\SapAutomation\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{},"https://pkgs.dev.azure.com/evolutionlab/_packaging/EvoLab_Feed/nuget/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.EntityFrameworkCore.Design":{"target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.Tools":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.Configuration":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.Configuration.Json":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.DependencyInjection":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Extensions.Hosting":{"target":"Package","version":"[9.0.0, )"},"Microsoft.Playwright":{"target":"Package","version":"[1.48.0, )"},"Polly":{"target":"Package","version":"[8.2.0, )"},"Serilog":{"target":"Package","version":"[4.0.1, )"},"Serilog.Extensions.Hosting":{"target":"Package","version":"[8.0.0, )"},"Serilog.Sinks.Console":{"target":"Package","version":"[6.0.0, )"},"Serilog.Sinks.File":{"target":"Package","version":"[6.0.0, )"},"Spectre.Console":{"target":"Package","version":"[0.49.1, )"},"Stateless":{"target":"Package","version":"[5.16.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}